<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi Tiết <PERSON>p <PERSON>on</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .class-header {
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            padding: 100px 0 50px;
            margin-top: 80px;
        }
        
        .class-info {
            text-align: center;
        }
        
        .class-info h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .class-schedule-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .schedule-detail {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
        }
        
        .class-content {
            padding: 50px 0;
        }
        
        .content-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            border-bottom: 1px solid #eee;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #4285F4;
            border-bottom-color: #4285F4;
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .lessons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .lesson-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s;
        }
        
        .lesson-card:hover {
            transform: translateY(-3px);
        }
        
        .lesson-number {
            background: #4285F4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .lesson-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .lesson-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .lesson-status {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-current {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-upcoming {
            background: #f8f9fa;
            color: #6c757d;
        }
        
        .students-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .student-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        
        .student-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            overflow: hidden;
        }
        
        .student-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .student-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .student-achievements {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 8px;
        }

        .achievement-badge {
            color: #ffd700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .achievement-badge:hover {
            transform: scale(1.2);
        }
        
        .student-progress {
            color: #666;
            font-size: 0.9rem;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .access-denied {
            text-align: center;
            padding: 50px;
            color: #721c24;
        }
        
        .access-denied i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #dc3545;
        }

        .meet-info {
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }

        .meet-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .meet-header h4 {
            color: #4285F4;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .meet-schedule {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
        }

        .meet-link {
            background: #4285F4;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .meet-link:hover {
            background: #3367D6;
            color: white;
        }

        .meet-dial {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .meet-dial h5 {
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Loading State -->
    <div id="loadingState" class="loading">
        <i class="fas fa-spinner fa-spin"></i> Đang tải thông tin lớp học...
    </div>

    <!-- Error Message -->
    <div id="errorMessage" class="error-message" style="display: none;"></div>

    <!-- Access Denied -->
    <div id="accessDenied" class="access-denied" style="display: none;">
        <i class="fas fa-lock"></i>
        <h2>Bạn không có quyền truy cập lớp học này!</h2>
        <p>Vui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.</p>
        <a href="index.html" style="color: #4285F4; text-decoration: none;">← Quay lại danh sách lớp học</a>
    </div>

    <!-- Class Header -->
    <div id="classHeader" class="class-header" style="display: none;">
        <div class="container">
            <div class="class-info">
                <h1 id="className">Python - A</h1>
                <div class="class-schedule-info">
                    <div class="schedule-detail">
                        <i class="fas fa-calendar-alt"></i>
                        <span id="classSchedule">Thứ 7 - Chủ Nhật</span>
                    </div>
                    <div class="schedule-detail">
                        <i class="fas fa-clock"></i>
                        <span id="classTime">19:30 - 21:00</span>
                    </div>
                    <div class="schedule-detail">
                        <i class="fas fa-users"></i>
                        <span id="classStudents">6/10 học viên</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Content -->
    <section id="classContent" class="class-content" style="display: none;">
        <div class="container">
            <div class="content-tabs">
                <button class="tab-button active" onclick="switchTab('lessons')">Bài Học</button>
                <button class="tab-button" onclick="switchTab('students')">Học Viên</button>
                <button class="tab-button" onclick="switchTab('assignments')">Bài Tập</button>
                <button class="tab-button" onclick="switchTab('meet')">Google Meet</button>
            </div>

            <!-- Lessons Tab -->
            <div id="lessonsTab" class="tab-content active">
                <h3>Chương trình học</h3>
                <div id="lessonsGrid" class="lessons-grid">
                    <!-- Lessons will be loaded dynamically -->
                </div>
            </div>

            <!-- Students Tab -->
            <div id="studentsTab" class="tab-content">
                <h3>Danh sách học viên</h3>
                <div id="studentsList" class="students-list">
                    <!-- Students will be loaded dynamically -->
                </div>
            </div>

            <!-- Assignments Tab -->
            <div id="assignmentsTab" class="tab-content">
                <h3>Bài tập và dự án</h3>
                <div id="assignmentsList" class="lessons-grid">
                    <!-- Assignments will be loaded dynamically -->
                </div>
            </div>

            <!-- Google Meet Tab -->
            <div id="meetTab" class="tab-content">
                <h3>Thông tin Google Meet</h3>
                <div id="meetInfo" class="meet-info">
                    <!-- Meet info will be loaded dynamically -->
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Get class ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const classId = urlParams.get('class');

        // UI Elements
        const loadingState = document.getElementById('loadingState');
        const errorMessage = document.getElementById('errorMessage');
        const accessDenied = document.getElementById('accessDenied');
        const classHeader = document.getElementById('classHeader');
        const classContent = document.getElementById('classContent');

        // Classes data
        const classesData = {
            'python-a': {
                name: 'Python - A',
                schedule: 'Thứ 7 - Chủ Nhật',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10,
                meetLink: 'https://meet.google.com/ysi-jixy-qms',
                meetPhone: '(US) ******-489-4345',
                meetPin: '710 784 803'
            },
            'python-b': {
                name: 'Python - B',
                schedule: 'Thứ 2 - Thứ 4',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10,
                meetLink: 'https://meet.google.com/example-link-b',
                meetPhone: '(US) ******-489-4345',
                meetPin: '123 456 789'
            },
            'python-c': {
                name: 'Python - C',
                schedule: 'Thứ 3 - Thứ 5',
                time: '19:30 - 21:00',
                students: 0, // Will be calculated from Firebase
                maxStudents: 10,
                meetLink: 'https://meet.google.com/pxs-hdwh-iqc',
                meetPhone: '(US) ******-574-1397',
                meetPin: '251 569 884'
            }
        };

        // Get students count for a specific class from Firebase
        async function getStudentsCount(classId) {
            try {
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", classId)
                );
                const querySnapshot = await getDocs(q);

                // Filter users who have fullName (profile completed)
                let count = 0;
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        count++;
                    }
                });

                return count;
            } catch (error) {
                console.error("Error getting students count:", error);
                return 0;
            }
        }

        // Get students data for a specific class from Firebase
        async function getStudentsData(classId) {
            try {
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", classId)
                );
                const querySnapshot = await getDocs(q);
                const students = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    // Only include users who have completed their profile
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            name: userData.fullName,
                            avatar: userData.avatar || '../assets/images/avatars/avatar_boy_1.png',
                            achievements: userData.achievements || [] // Student achievements
                        });
                    }
                });

                return students;
            } catch (error) {
                console.error("Error getting students data:", error);
                return [];
            }
        }

        // Lessons data for each class
        const lessonsData = {
            'python-a': [
                {
                    number: 1,
                    title: "Chào Mừng Đến Với Kỷ Nguyên Số và Thế Giới Lập Trình",
                    description: "Tổng quan về CNTT và giới thiệu lập trình",
                    status: "current", // Will be updated based on assignment completion
                    link: "../lessons/python-a/lesson-1.html",
                    assignmentId: "assignment-1"
                }
            ],
            'python-b': [
                // No lessons yet
            ],
            'python-c': [
                {
                    number: 1,
                    title: "Chào Mừng Đến Với Kỷ Nguyên Số và Thế Giới Lập Trình",
                    description: "Tổng quan về CNTT và giới thiệu lập trình",
                    status: "current", // Will be updated based on assignment completion
                    link: "../lessons/python-c/lesson-1.html",
                    assignmentId: "assignment-1"
                }
            ]
        };

        // Check if user has completed assignment for a lesson
        async function checkLessonCompletion(lessonNumber) {
            const user = auth.currentUser;
            if (!user) return false;

            try {
                const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", `assignment-${lessonNumber}`));
                return assignmentDoc.exists();
            } catch (error) {
                console.error("Error checking lesson completion:", error);
                return false;
            }
        }

        // Check if user is admin
        function isAdmin(user) {
            return user && user.email === '<EMAIL>';
        }

        // Check access and load class
        async function checkAccessAndLoadClass(user) {
            if (!classId) {
                showError('Không tìm thấy thông tin lớp học');
                return;
            }

            if (!user) {
                showAccessDenied();
                return;
            }

            // Admin has access to all classes
            if (isAdmin(user)) {
                console.log('Admin access granted to class:', classId);
                loadClassDetails();
                return;
            }

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    if (userData.courseClass === classId) {
                        loadClassDetails();
                    } else {
                        showAccessDenied();
                    }
                } else {
                    showAccessDenied();
                }
            } catch (error) {
                console.error("Error checking access:", error);
                showError('Lỗi khi kiểm tra quyền truy cập: ' + error.message);
            }
        }

        function showError(message) {
            loadingState.style.display = 'none';
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function showAccessDenied() {
            loadingState.style.display = 'none';
            accessDenied.style.display = 'block';
        }

        async function loadClassDetails() {
            const classData = classesData[classId];
            if (!classData) {
                showError('Không tìm thấy thông tin lớp học');
                return;
            }

            // Get real students count from Firebase
            const studentsCount = await getStudentsCount(classId);

            // Update header
            document.getElementById('className').textContent = classData.name;
            document.getElementById('classSchedule').textContent = classData.schedule;
            document.getElementById('classTime').textContent = classData.time;
            document.getElementById('classStudents').textContent = `${studentsCount}/${classData.maxStudents} học viên`;

            // Load lessons
            loadLessons();
            loadStudents();
            loadAssignments();
            loadMeetInfo();

            // Show content
            loadingState.style.display = 'none';
            classHeader.style.display = 'block';
            classContent.style.display = 'block';
        }

        async function loadLessons() {
            const lessonsGrid = document.getElementById('lessonsGrid');
            const classLessons = lessonsData[classId] || [];
            let lessonsHTML = '';

            if (classLessons.length > 0) {
                for (const lesson of classLessons) {
                    // Check if lesson is completed based on assignment submission
                    const isCompleted = await checkLessonCompletion(lesson.number);
                    const status = isCompleted ? 'completed' : lesson.status;

                    const statusClass = `status-${status}`;
                    const statusText = {
                        'completed': 'Đã hoàn thành',
                        'current': 'Đang học',
                        'upcoming': 'Sắp tới'
                    }[status];

                    const lessonContent = lesson.link ?
                        `<a href="${lesson.link}" style="text-decoration: none; color: inherit;">
                            <div class="lesson-card" style="cursor: pointer; transition: transform 0.3s;">
                                <div class="lesson-number">${lesson.number}</div>
                                <div class="lesson-title">${lesson.title}</div>
                                <div class="lesson-description">${lesson.description}</div>
                                <span class="lesson-status ${statusClass}">${statusText}</span>
                            </div>
                        </a>` :
                        `<div class="lesson-card">
                            <div class="lesson-number">${lesson.number}</div>
                            <div class="lesson-title">${lesson.title}</div>
                            <div class="lesson-description">${lesson.description}</div>
                            <span class="lesson-status ${statusClass}">${statusText}</span>
                        </div>`;

                    lessonsHTML += lessonContent;
                }
            } else {
                lessonsHTML = '<p style="text-align: center; color: #666; grid-column: 1/-1;">Chưa có bài học nào được thêm vào lớp này</p>';
            }

            lessonsGrid.innerHTML = lessonsHTML;
        }

        async function loadStudents() {
            const studentsList = document.getElementById('studentsList');

            // Get real students data from Firebase
            const studentsData = await getStudentsData(classId);
            let studentsHTML = '';

            if (studentsData.length > 0) {
                studentsData.forEach(student => {
                    // Display achievements (medals/badges) - placeholder for future implementation
                    let achievementsHTML = '';
                    if (student.achievements && student.achievements.length > 0) {
                        achievementsHTML = `
                            <div class="student-achievements">
                                ${student.achievements.map(achievement => `
                                    <span class="achievement-badge" title="${achievement.title}">
                                        <i class="fas fa-medal"></i>
                                    </span>
                                `).join('')}
                            </div>
                        `;
                    }

                    studentsHTML += `
                        <div class="student-card">
                            <div class="student-avatar-large">
                                <img src="${student.avatar}" alt="${student.name}">
                            </div>
                            <div class="student-name">${student.name}</div>
                            ${achievementsHTML}
                        </div>
                    `;
                });
            } else {
                studentsHTML = '<p style="text-align: center; color: #666; grid-column: 1/-1;">Lớp học chưa có học viên nào</p>';
            }

            studentsList.innerHTML = studentsHTML;
        }

        async function loadAssignments() {
            const assignmentsList = document.getElementById('assignmentsList');

            // Assignments data for each class
            const assignmentsData = {
                'python-a': [
                    {
                        number: 1,
                        title: "Bài tập 1: Trắc Nghiệm CNTT và Lập Trình",
                        description: "30 câu hỏi trắc nghiệm về CNTT và lập trình cơ bản",
                        deadline: "Chưa nộp", // Will be updated based on completion
                        link: "../assignments/python-a/assignment-1.html",
                        type: "quiz",
                        assignmentId: "assignment-1"
                    }
                ],
                'python-b': [
                    // No assignments yet
                ],
                'python-c': [
                    {
                        number: 1,
                        title: "Bài tập 1: Trắc Nghiệm CNTT và Lập Trình",
                        description: "30 câu hỏi trắc nghiệm về CNTT và lập trình cơ bản",
                        deadline: "Chưa nộp", // Will be updated based on completion
                        link: "../assignments/python-c/assignment-1.html",
                        type: "quiz",
                        assignmentId: "assignment-1"
                    }
                ]
            };

            const classAssignments = assignmentsData[classId] || [];
            let assignmentsHTML = '';

            if (classAssignments.length > 0) {
                for (const assignment of classAssignments) {
                    // Check if assignment is completed
                    const isCompleted = await checkLessonCompletion(assignment.number);
                    const status = isCompleted ? 'Đã nộp' : assignment.deadline;

                    let assignmentContent;
                    if (assignment.link && !isCompleted) {
                        // Only allow access if not completed yet
                        assignmentContent = `<a href="${assignment.link}" style="text-decoration: none; color: inherit;">
                            <div class="lesson-card" style="cursor: pointer; transition: transform 0.3s;">
                                <div class="lesson-number">${assignment.number}</div>
                                <div class="lesson-title">${assignment.title}</div>
                                <div class="lesson-description">${assignment.description}</div>
                                <span class="lesson-status ${status.includes('Đã') ? 'status-completed' : 'status-current'}">${status}</span>
                            </div>
                        </a>`;
                    } else {
                        assignmentContent = `<div class="lesson-card">
                            <div class="lesson-number">${assignment.number}</div>
                            <div class="lesson-title">${assignment.title}</div>
                            <div class="lesson-description">${assignment.description}</div>
                            <span class="lesson-status ${status.includes('Đã') ? 'status-completed' : 'status-current'}">${status}</span>
                        </div>`;
                    }

                    assignmentsHTML += assignmentContent;
                }
            } else {
                assignmentsHTML = '<p style="text-align: center; color: #666; grid-column: 1/-1;">Chưa có bài tập nào được thêm vào lớp này</p>';
            }

            assignmentsList.innerHTML = assignmentsHTML;
        }

        function loadMeetInfo() {
            const meetInfo = document.getElementById('meetInfo');
            const classData = classesData[classId];

            if (!classData) return;

            const meetHTML = `
                <div class="meet-header">
                    <h4>${classData.name}</h4>
                    <div class="meet-schedule">
                        <p><strong>Lịch học:</strong> ${classData.schedule}</p>
                        <p><strong>Thời gian:</strong> ${classData.time}</p>
                        <p><strong>Múi giờ:</strong> Asia/Ho_Chi_Minh</p>
                    </div>
                </div>

                <div style="text-align: center;">
                    <h5>Thông tin tham gia Google Meet</h5>

                    <a href="${classData.meetLink}" target="_blank" class="meet-link">
                        <i class="fas fa-video"></i> Tham gia cuộc họp video
                    </a>

                    <div class="meet-dial">
                        <h5>Hoặc gọi điện thoại:</h5>
                        <p><strong>Số điện thoại:</strong> ${classData.meetPhone}</p>
                        <p><strong>PIN:</strong> ${classData.meetPin}#</p>
                    </div>
                </div>
            `;

            meetInfo.innerHTML = meetHTML;
        }

        // Tab switching
        window.switchTab = function(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
        };

        // Auth state change listener
        onAuthStateChanged(auth, (user) => {
            checkAccessAndLoadClass(user);
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
